# Cache Strategy for Unified Database System

## Overview

The unified database system is designed to work as an intelligent cache layer that provides fast, reliable access to aggregated data from multiple sources while ensuring data freshness and quality.

## Cache Architecture

### Multi-Level Caching
```
┌─────────────────┐
│   Memory Cache  │ ← Fastest (sub-millisecond)
├─────────────────┤
│ Unified Database│ ← Fast (milliseconds) 
├─────────────────┤
│ Source Databases│ ← Fallback (seconds)
└─────────────────┘
```

### Cache Flow Strategy

#### 1. Cache-First Approach
```python
async def get_data(currency_pair, timeframe, candles):
    # Step 1: Check memory cache
    if memory_cache.has_fresh_data():
        return memory_cache.get_data()
    
    # Step 2: Check unified database cache
    cache_result = await check_unified_cache()
    
    if cache_result['cache_hit_ratio'] >= 0.8:
        # Sufficient cache - return immediately
        return cache_result['data']
    
    elif cache_result['cache_hit_ratio'] > 0.3:
        # Partial cache - return what we have + background update
        asyncio.create_task(background_update())
        return cache_result['data']
    
    else:
        # Cache miss - fetch fresh data synchronously
        fresh_data = await fetch_from_sources()
        await update_cache(fresh_data)
        return fresh_data
```

## Cache Strategies

### 1. Cache-Only
- **Use case**: When speed is critical, data freshness less important
- **Behavior**: Only return cached data, never fetch from sources
- **Fallback**: Return stale data or error if no cache

### 2. Cache-First (Recommended)
- **Use case**: Balance of speed and freshness
- **Behavior**: Prefer cache, fetch missing data, background updates
- **Fallback**: Fetch from sources if cache insufficient

### 3. Fresh-First
- **Use case**: When data freshness is critical
- **Behavior**: Always try sources first, use cache as fallback
- **Fallback**: Use cache if sources fail

### 4. Background Refresh
- **Use case**: Continuous data updates
- **Behavior**: Return cache immediately, always refresh in background
- **Fallback**: Cache-only if background updates fail

## Freshness Management

### Timeframe-Specific TTL
```python
FRESHNESS_RULES = {
    'm1': {
        'max_age_minutes': 2,        # 1-min data stale after 2 minutes
        'quality_bonus_minutes': 1,   # High-quality data gets +1 minute
        'market_hours_factor': 1.5    # 50% longer TTL during off-hours
    },
    'h1': {
        'max_age_minutes': 61,       # 1-hour data stale after 61 minutes
        'quality_bonus_minutes': 15,  # High-quality data gets +15 minutes
        'market_hours_factor': 1.2    # 20% longer TTL during off-hours
    }
}
```

### Quality-Aware Caching
- **High-quality data** (score > 0.9): Cached longer
- **Medium-quality data** (score 0.7-0.9): Standard TTL
- **Low-quality data** (score < 0.7): Shorter TTL, prioritize refresh

### Market Hours Consideration
- **Active trading hours**: Shorter TTL for faster updates
- **Off-hours/weekends**: Longer TTL to reduce unnecessary API calls

## Background Updates

### Non-Blocking Updates
```python
async def background_update_task(missing_timestamps):
    """Update cache without blocking current requests"""
    try:
        # Fetch missing data from sources
        fresh_data = await source_coordinator.fetch_missing_data(missing_timestamps)
        
        # Update cache
        await cache_manager.cache_data(fresh_data)
        
        # Log success
        logger.info(f"Background update completed: {len(fresh_data)} records")
        
    except Exception as e:
        # Log error but don't affect current requests
        logger.error(f"Background update failed: {e}")
```

### Update Prioritization
1. **High priority**: Recently requested data
2. **Medium priority**: Popular currency pairs
3. **Low priority**: Rarely accessed data

### Queue Management
- **Max concurrent updates**: 3 (configurable)
- **Queue size limit**: 100 tasks
- **Retry logic**: 3 attempts with exponential backoff

## Cache Warming

### Proactive Cache Population
```python
# Warm cache for popular data every 30 minutes
WARMING_SCHEDULE = {
    'popular_pairs': ['btcusd', 'ethusd', 'ethbtc'],
    'timeframes': ['m1', 'm5', 'm15', 'h1'],
    'interval_minutes': 30,
    'candles_to_warm': 100  # Keep last 100 candles warm
}
```

### Warming Strategies
1. **Time-based**: Warm cache at regular intervals
2. **Usage-based**: Warm frequently accessed data
3. **Predictive**: Warm data likely to be requested soon

## Performance Optimizations

### Memory Cache (LRU)
- **Size**: 1000 entries (configurable)
- **Eviction**: Least Recently Used
- **Access time**: < 1ms
- **Use case**: Very recent requests

### Database Cache Optimizations
- **Indexes**: Optimized for (currency_pair, timeframe, timestamp)
- **Query optimization**: Prepared statements, connection pooling
- **Access time**: 1-10ms
- **Use case**: Recent and popular data

### Batch Operations
- **Batch inserts**: Update multiple records in single transaction
- **Batch queries**: Fetch multiple timeframes together
- **Connection pooling**: Reuse database connections

## Monitoring and Metrics

### Cache Performance Metrics
```python
cache_metrics = {
    'hit_ratio': 0.85,              # 85% cache hit rate
    'avg_response_time_ms': 5,      # Average response time
    'memory_usage_mb': 150,         # Memory cache usage
    'db_cache_size_mb': 2048,       # Database cache size
    'background_update_rate': 0.95,  # Background update success rate
    'stale_data_percentage': 0.05    # Percentage of stale data served
}
```

### Health Checks
- **Cache hit ratio** > 80% (healthy)
- **Average response time** < 100ms
- **Background update success rate** > 90%
- **Memory usage** within limits

## Configuration Examples

### High-Performance Setup (Trading Platform)
```python
CACHE_STRATEGY = 'cache_first'
CACHE_HIT_THRESHOLD = 0.9
MEMORY_CACHE_SIZE = 2000
FRESHNESS_RULES = {
    'm1': {'max_age_minutes': 1},  # Very fresh data
    'm5': {'max_age_minutes': 3}
}
```

### Balanced Setup (General API)
```python
CACHE_STRATEGY = 'cache_first'
CACHE_HIT_THRESHOLD = 0.8
MEMORY_CACHE_SIZE = 1000
FRESHNESS_RULES = {
    'm1': {'max_age_minutes': 2},  # Standard freshness
    'h1': {'max_age_minutes': 61}
}
```

### Conservative Setup (Analytics)
```python
CACHE_STRATEGY = 'background'
CACHE_HIT_THRESHOLD = 0.7
MEMORY_CACHE_SIZE = 500
FRESHNESS_RULES = {
    'h1': {'max_age_minutes': 120},  # Longer TTL acceptable
    'd1': {'max_age_minutes': 2880}  # 2-day TTL for daily data
}
```

## Implementation Benefits

### For Users
- **Fast responses**: Sub-second for cached data
- **High availability**: Works even when sources are slow
- **Consistent performance**: Predictable response times

### For System
- **Reduced API calls**: Fewer requests to external sources
- **Better resource utilization**: Efficient use of bandwidth and CPU
- **Improved reliability**: Graceful degradation during outages

### For Developers
- **Simple interface**: Cache complexity hidden from endpoints
- **Easy configuration**: Tune cache behavior without code changes
- **Rich monitoring**: Comprehensive metrics and health checks

This cache strategy transforms the unified database from a simple storage layer into an intelligent, high-performance caching system that provides the best possible user experience while maintaining data quality and system reliability.
