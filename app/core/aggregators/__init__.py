"""
Aggregators Package - Data aggregation components for unified data processing

This package contains all data aggregation components responsible for combining
data from multiple sources into unified, high-quality datasets.

Modules:
- base_aggregator.py: Abstract base class for all aggregators
- candles_aggregator.py: Aggregates candle data from multiple sources
- range_aggregator.py: Aggregates range data from multiple sources  
- data_quality_checker.py: Validates and scores data quality

Key Concepts:
1. Data Aggregation: Combining data from multiple sources intelligently
2. Quality Assessment: Scoring data quality based on various metrics
3. Conflict Resolution: Handling discrepancies between sources
4. Gap Filling: Using multiple sources to fill missing data points

Aggregation Strategies:
- Quality Weighted: Prioritize data from higher quality sources
- Volume Weighted: Weight data based on trading volume
- Simple Average: Average values across all sources
- Best Source: Use data from the most reliable source for each timestamp

Quality Metrics:
- Completeness: Percentage of expected data points present
- Consistency: Agreement between sources
- Timeliness: How recent the data is
- Volume Correlation: How well volume data correlates between sources
- Price Reasonableness: Detection of obvious outliers

Future Enhancements:
- Machine learning for quality prediction
- Real-time quality monitoring
- Advanced outlier detection
- Source reliability scoring
"""

# Package-level constants and configurations
AGGREGATION_STRATEGIES = [
    'quality_weighted',
    'volume_weighted', 
    'simple_average',
    'best_source'
]

QUALITY_METRICS = [
    'completeness',
    'consistency',
    'timeliness',
    'volume_correlation',
    'price_reasonableness'
]

# Default quality thresholds
DEFAULT_QUALITY_THRESHOLDS = {
    'completeness': 0.95,      # 95% of expected data points
    'consistency': 0.90,       # 90% agreement between sources
    'timeliness': 300,         # Data not older than 5 minutes
    'volume_correlation': 0.80, # 80% volume correlation
    'price_reasonableness': 0.05 # 5% maximum price deviation
}

# Source reliability scores (can be dynamically updated)
DEFAULT_SOURCE_RELIABILITY = {
    'bitstamp': 0.90,
    'coindesk': 0.85,
    'kraken': 0.88
}
