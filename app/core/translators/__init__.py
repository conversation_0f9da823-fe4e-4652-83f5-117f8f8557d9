"""
Translators Package - Source-specific data translators for unified format conversion

This package contains all translator components responsible for converting
source-specific data formats into the unified format used throughout the system.

Modules:
- base_translator.py: Abstract base class for all translators
- bitstamp_translator.py: Translator for Bitstamp API data
- coindesk_translator.py: Translator for Coindesk API data
- kraken_translator.py: Translator for Kraken API data (future)

Key Concepts:
1. Data Translation: Converting source-specific formats to unified format
2. Schema Normalization: Ensuring consistent column names and data types
3. Timestamp Standardization: Converting to UTC timestamps
4. Data Validation: Ensuring translated data meets quality standards
5. Error Handling: Graceful handling of malformed source data

Unified Data Format:
All translators convert source data to this standard format:
```
{
    'timestamp': int64,      # UTC timestamp
    'date': datetime,        # Human-readable datetime
    'open': float64,         # Opening price
    'high': float64,         # Highest price
    'low': float64,          # Lowest price
    'close': float64,        # Closing price
    'volume': float64,       # Trading volume
    'source': str,           # Source identifier
    'quality_score': float64 # Data quality score (0-1)
}
```

Translation Challenges:
- Different timestamp formats (Unix, ISO, custom)
- Different data types (string vs numeric)
- Different column names and ordering
- Different precision levels
- Missing or null values
- Source-specific quirks and edge cases

Quality Assessment:
Each translator assesses data quality during translation:
- Data completeness
- Value reasonableness
- Format compliance
- Source-specific validation rules

Future Enhancements:
- Automatic schema detection
- Machine learning for quality scoring
- Real-time translation monitoring
- Advanced error recovery
"""

# Unified data schema definition
UNIFIED_SCHEMA = {
    'timestamp': 'int64',
    'date': 'datetime64[ns]',
    'open': 'float64',
    'high': 'float64', 
    'low': 'float64',
    'close': 'float64',
    'volume': 'float64',
    'source': 'object',
    'quality_score': 'float64'
}

# Required columns for all translated data
REQUIRED_COLUMNS = [
    'timestamp',
    'date', 
    'open',
    'high',
    'low',
    'close',
    'volume'
]

# Optional columns that may be added during translation
OPTIONAL_COLUMNS = [
    'source',
    'quality_score',
    'raw_data',
    'translation_metadata'
]

# Source-specific configurations
SOURCE_CONFIGS = {
    'bitstamp': {
        'timestamp_format': 'unix',
        'timezone': 'UTC',
        'decimal_precision': 8,
        'volume_unit': 'base_currency'
    },
    'coindesk': {
        'timestamp_format': 'iso',
        'timezone': 'UTC', 
        'decimal_precision': 8,
        'volume_unit': 'base_currency'
    },
    'kraken': {
        'timestamp_format': 'unix',
        'timezone': 'UTC',
        'decimal_precision': 8,
        'volume_unit': 'base_currency'
    }
}
