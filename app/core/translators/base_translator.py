"""
Base Translator - Abstract base class for all data translators

This module defines the interface and common functionality for all data translators
in the dabot-ohlc system. It provides a consistent API for converting source-specific
data formats into the unified format.

Key Responsibilities:
1. Define the standard interface for all translators
2. Provide common utility methods for data conversion
3. Implement base validation and quality assessment
4. Handle common error scenarios
5. Provide logging and monitoring capabilities

Design Patterns:
- Template Method: Define translation algorithm structure
- Strategy Pattern: Allow different translation strategies
- Factory Pattern: Create appropriate translators

Translation Process:
1. Validate input data format
2. Convert timestamps to unified format
3. Normalize column names and data types
4. Validate OHLC relationships
5. Calculate quality score
6. Add metadata and source information
7. Return standardized DataFrame

Quality Assessment:
- Data completeness check
- Value range validation
- Format compliance verification
- Source-specific validation rules

Error Handling:
- Graceful handling of malformed data
- Comprehensive error logging
- Fallback mechanisms for partial data
- Data recovery strategies
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from datetime import datetime

from app.logger.get_logger import log, logger
from app.core.translators import UNIFIED_SCHEMA, REQUIRED_COLUMNS


class BaseTranslator:
    """
    Abstract base class for all data translators.

    This class defines the common interface and provides shared functionality
    for translating source-specific data formats into the unified format.
    """

    def __init__(self, source_name: str):
        """
        Initialize the base translator.

        Args:
            source_name: Name of the data source (e.g., 'bitstamp')
        """
        self._source_name = source_name
        self._translation_stats = {
            'records_processed': 0,
            'successful_translations': 0,
            'failed_translations': 0,
            'quality_scores': []
        }

    def translate(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """
        Main translation method - template method pattern.

        This method defines the overall translation algorithm:
        1. Validate input data
        2. Preprocess raw data
        3. Convert timestamps
        4. Normalize columns
        5. Validate OHLC data
        6. Calculate quality score
        7. Add metadata
        8. Post-process result

        Args:
            raw_data: Raw DataFrame from source API

        Returns:
            DataFrame in unified format
        """
        try:
            self._translation_stats['records_processed'] += len(raw_data) if raw_data is not None else 0

            # Step 1: Validate input data
            if not self._validate_input_data(raw_data):
                logger.error(f"Input validation failed for {self._source_name}")
                return pd.DataFrame()

            # Step 2: Preprocess raw data
            preprocessed_data = self._preprocess_raw_data(raw_data)

            # Step 3: Convert timestamps
            timestamped_data = self._convert_timestamps(preprocessed_data)

            # Step 4: Normalize columns
            normalized_data = self._normalize_columns(timestamped_data)

            # Step 5: Validate OHLC data
            is_valid, errors = self._validate_ohlc_data(normalized_data)
            if not is_valid:
                logger.warning(f"OHLC validation issues for {self._source_name}: {errors}")

            # Step 6: Calculate quality score
            quality_scores = self._calculate_quality_score(normalized_data, (is_valid, errors))

            # Step 7: Add metadata
            data_with_metadata = self._add_metadata(normalized_data)
            data_with_metadata['quality_score'] = quality_scores

            # Step 8: Post-process result
            final_data = self._post_process(data_with_metadata)

            self._translation_stats['successful_translations'] += len(final_data)
            self._translation_stats['quality_scores'].extend(quality_scores.tolist())

            return final_data

        except Exception as e:
            logger.error(f"Translation failed for {self._source_name}: {str(e)}")
            self._translation_stats['failed_translations'] += 1
            return pd.DataFrame()

    def _validate_input_data(self, raw_data: pd.DataFrame) -> bool:
        """
        Validate input data from source.

        Common validation checks:
        - DataFrame is not empty
        - Required columns are present
        - Data types are reasonable
        - No completely null rows

        Args:
            raw_data: Raw data to validate

        Returns:
            bool: True if data is valid for translation
        """
        if raw_data is None or raw_data.empty:
            logger.error(f"Empty or None data for {self._source_name}")
            return False

        # Check for basic OHLC columns (names may vary by source)
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        available_cols = raw_data.columns.tolist()

        # Check if we have timestamp column (various names)
        timestamp_cols = ['timestamp', 'time', 'date']
        has_timestamp = any(col in available_cols for col in timestamp_cols)

        # Check if we have OHLC columns
        ohlc_cols = ['open', 'high', 'low', 'close']
        has_ohlc = all(col in available_cols for col in ohlc_cols)

        if not has_timestamp:
            logger.error(f"No timestamp column found in {self._source_name} data")
            return False

        if not has_ohlc:
            logger.error(f"Missing OHLC columns in {self._source_name} data")
            return False

        return True

    def _preprocess_raw_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """
        Preprocess raw data before translation.

        Common preprocessing steps:
        - Remove null/empty rows
        - Handle missing values
        - Basic data cleaning
        - Sort by timestamp

        Args:
            raw_data: Raw data from source

        Returns:
            Preprocessed DataFrame
        """
        data = raw_data.copy()

        # Remove completely empty rows
        data = data.dropna(how='all')

        # Remove rows where all OHLC values are null
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in data.columns]
        if available_ohlc:
            data = data.dropna(subset=available_ohlc, how='all')

        # Reset index
        data = data.reset_index(drop=True)

        return data

    @abstractmethod
    def _convert_timestamps(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Convert source-specific timestamps to unified format.

        This method must be implemented by each source translator
        to handle their specific timestamp format.

        Args:
            data: DataFrame with source timestamps

        Returns:
            DataFrame with unified timestamp format

        TODO: Implement in source-specific translators
        """
        pass

    @abstractmethod
    def _normalize_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize column names and data types to unified format.

        This method must be implemented by each source translator
        to handle their specific column naming and data types.

        Args:
            data: DataFrame with source-specific columns

        Returns:
            DataFrame with unified column format

        TODO: Implement in source-specific translators
        """
        pass

    def _validate_ohlc_data(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate OHLC data relationships and ranges.

        Validation checks:
        - High >= max(Open, Close)
        - Low <= min(Open, Close)
        - All prices are positive
        - Volume is non-negative

        Args:
            data: DataFrame with OHLC data

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []

        if data.empty:
            return False, ["Empty DataFrame"]

        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            errors.append(f"Missing columns: {missing_cols}")
            return False, errors

        # Check for positive prices
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            if (data[col] <= 0).any():
                errors.append(f"Non-positive values found in {col}")

        # Check OHLC relationships
        high_low_valid = (data['high'] >= data['low']).all()
        if not high_low_valid:
            errors.append("High < Low relationship violation")

        high_open_valid = (data['high'] >= data['open']).all()
        if not high_open_valid:
            errors.append("High < Open relationship violation")

        high_close_valid = (data['high'] >= data['close']).all()
        if not high_close_valid:
            errors.append("High < Close relationship violation")

        low_open_valid = (data['low'] <= data['open']).all()
        if not low_open_valid:
            errors.append("Low > Open relationship violation")

        low_close_valid = (data['low'] <= data['close']).all()
        if not low_close_valid:
            errors.append("Low > Close relationship violation")

        # Check volume if present
        if 'volume' in data.columns:
            if (data['volume'] < 0).any():
                errors.append("Negative volume values found")

        return len(errors) == 0, errors

    def _calculate_quality_score(self, data: pd.DataFrame,
                                validation_results: Tuple[bool, List[str]]) -> pd.Series:
        """
        Calculate quality score for each data point.

        Quality factors:
        - Data completeness (no null values)
        - OHLC relationship validity
        - Value reasonableness
        - Source-specific quality indicators

        Args:
            data: Translated DataFrame
            validation_results: Results from OHLC validation

        Returns:
            Series with quality scores (0-1) for each row
        """
        if data.empty:
            return pd.Series(dtype=float)

        is_valid, errors = validation_results
        base_score = 0.8 if is_valid else 0.6

        # Calculate per-row quality scores
        quality_scores = pd.Series([base_score] * len(data), index=data.index)

        # Bonus for data completeness
        required_cols = ['timestamp', 'open', 'high', 'low', 'close']
        available_cols = [col for col in required_cols if col in data.columns]

        for idx, row in data.iterrows():
            score = base_score

            # Completeness bonus (0.1)
            if not row[available_cols].isnull().any():
                score += 0.1

            # OHLC relationship bonus (0.1)
            if (row['high'] >= max(row['open'], row['close']) and
                row['low'] <= min(row['open'], row['close'])):
                score += 0.1

            # Volume presence bonus (0.05)
            if 'volume' in data.columns and pd.notna(row['volume']) and row['volume'] > 0:
                score += 0.05

            quality_scores.iloc[idx] = min(score, 1.0)  # Cap at 1.0

        return quality_scores

    def _add_metadata(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add metadata columns to translated data.

        Metadata includes:
        - Source identifier
        - Translation timestamp
        - Quality scores
        - Any source-specific metadata

        Args:
            data: Translated DataFrame

        Returns:
            DataFrame with metadata columns added
        """
        if data.empty:
            return data

        data_with_metadata = data.copy()

        # Add source identifier
        data_with_metadata['source'] = self._source_name

        # Add translation timestamp
        data_with_metadata['translated_at'] = datetime.utcnow()

        return data_with_metadata

    def _post_process(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Post-process translated data before returning.

        Final steps:
        - Sort by timestamp
        - Remove duplicates
        - Apply final validation
        - Optimize data types

        Args:
            data: Translated data with metadata

        Returns:
            Final processed DataFrame
        """
        if data.empty:
            return data

        processed_data = data.copy()

        # Sort by timestamp
        if 'timestamp' in processed_data.columns:
            processed_data = processed_data.sort_values('timestamp').reset_index(drop=True)

        # Remove duplicates based on timestamp
        if 'timestamp' in processed_data.columns:
            processed_data = processed_data.drop_duplicates(subset=['timestamp'], keep='last')

        # Ensure proper column order
        column_order = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume',
                       'source', 'quality_score', 'translated_at']
        available_columns = [col for col in column_order if col in processed_data.columns]
        other_columns = [col for col in processed_data.columns if col not in column_order]

        final_columns = available_columns + other_columns
        processed_data = processed_data[final_columns]

        return processed_data

    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Handle missing values in source data.

        Strategies:
        - Forward fill for minor gaps
        - Interpolation for OHLC values
        - Zero fill for volume (conservative)
        - Mark quality score appropriately

        Args:
            data: DataFrame with potential missing values

        Returns:
            DataFrame with missing values handled

        TODO: Implement missing value handling
        """
        pass

    def _detect_and_fix_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Detect and fix obvious outliers in source data.

        Outlier handling:
        - Detect extreme values
        - Apply source-specific correction rules
        - Mark corrected data in quality score
        - Log corrections made

        Args:
            data: DataFrame with potential outliers

        Returns:
            DataFrame with outliers handled

        TODO: Implement outlier detection and correction
        """
        pass

    def get_translation_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about translation performance.

        Returns:
            Dict with translation statistics:
            - Number of records processed
            - Success/failure rates
            - Quality score distribution
            - Common errors encountered

        TODO: Implement statistics collection
        """
        pass

    def validate_output_schema(self, data: pd.DataFrame) -> bool:
        """
        Validate that output data conforms to unified schema.

        Checks:
        - All required columns present
        - Correct data types
        - No null values in required columns
        - Schema compliance

        Args:
            data: Translated DataFrame to validate

        Returns:
            bool: True if schema is valid

        TODO: Implement schema validation
        """
        pass

    def get_translation_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about translation performance.

        Returns:
            Dict with translation statistics
        """
        stats = self._translation_stats.copy()
        if stats['quality_scores']:
            stats['avg_quality_score'] = np.mean(stats['quality_scores'])
            stats['min_quality_score'] = np.min(stats['quality_scores'])
            stats['max_quality_score'] = np.max(stats['quality_scores'])
        else:
            stats['avg_quality_score'] = 0.0
            stats['min_quality_score'] = 0.0
            stats['max_quality_score'] = 0.0

        return stats

    @property
    def source_name(self) -> str:
        """Get the name of the data source."""
        return self._source_name

    @property
    @abstractmethod
    def supported_timeframes(self) -> List[str]:
        """Get list of timeframes supported by this source."""
        pass
