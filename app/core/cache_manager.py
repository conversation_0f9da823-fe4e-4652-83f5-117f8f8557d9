"""
Cache Manager - Intelligent caching system for unified data

This module implements a sophisticated caching system that treats the unified
database as an intelligent cache layer. It provides fast data access while
ensuring data freshness and quality.

Key Responsibilities:
1. Implement cache-first data retrieval strategy
2. Manage cache freshness and expiration policies
3. Handle cache warming and background updates
4. Optimize cache hit ratios and performance
5. Provide cache statistics and monitoring
6. Handle cache invalidation and cleanup

Cache Strategy:
- Cache-first: Always check cache before fetching from sources
- Lazy loading: Fetch missing data on demand
- Background refresh: Update stale data asynchronously
- Intelligent prefetching: Predict and cache likely requests
- Quality-aware caching: Cache high-quality data longer

Cache Levels:
1. Memory Cache: Fast in-memory cache for recent data
2. Unified Database: Persistent cache with aggregated data
3. Source Databases: Fallback cache with raw source data

Performance Features:
- Sub-second response times for cached data
- Background updates don't block requests
- Intelligent cache warming strategies
- Memory-efficient cache management
- Optimized database queries

Cache Policies:
- TTL (Time To Live) based on timeframe
- LRU (Least Recently Used) eviction
- Quality-based retention
- Size-based limits
- Access pattern optimization
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import time

from app.logger.get_logger import log, logger
from app.config import CACHE_EXPIRY_MINUTES, DATA_QUALITY_THRESHOLD


class CacheStrategy(Enum):
    """Cache strategy options."""
    CACHE_ONLY = "cache_only"          # Return only cached data
    CACHE_FIRST = "cache_first"        # Prefer cache, fetch if missing
    FRESH_FIRST = "fresh_first"        # Prefer fresh data, use cache as fallback
    BACKGROUND_REFRESH = "background"   # Return cache, refresh in background


@dataclass
class CacheEntry:
    """
    Represents a cache entry with metadata.
    """
    data: pd.DataFrame
    timestamp: datetime
    quality_score: float
    source_count: int
    expiry_time: datetime
    access_count: int = 0
    last_accessed: datetime = None

    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = datetime.utcnow()

    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        return datetime.utcnow() > self.expiry_time

    def is_fresh(self) -> bool:
        """Check if cache entry is still fresh."""
        return not self.is_expired()

    def access(self):
        """Mark entry as accessed."""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()


class CacheManager:
    """
    Intelligent cache manager for unified data system.

    This class implements a sophisticated caching strategy that treats
    the unified database as a smart cache layer with multiple levels
    and intelligent refresh policies.
    """

    def __init__(self, max_memory_entries: int = 1000):
        """
        Initialize the cache manager.

        Args:
            max_memory_entries: Maximum entries in memory cache
        """
        # Memory cache (simple dict for now, can be upgraded to LRU)
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._max_memory_entries = max_memory_entries

        # Cache statistics
        self._stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'memory_hits': 0,
            'database_hits': 0,
            'background_updates': 0,
            'cache_evictions': 0
        }

        # Background task tracking
        self._background_tasks: Dict[str, asyncio.Task] = {}

        logger.info(f"CacheManager initialized with max_memory_entries={max_memory_entries}")

    async def get_cached_data(self, currency_pair: str, timeframe: str,
                             candles: int, strategy: CacheStrategy = CacheStrategy.CACHE_FIRST) -> Dict[str, Any]:
        """
        Get data using specified cache strategy.

        Main entry point for cache-aware data retrieval:
        1. Check memory cache first
        2. Check unified database cache
        3. Determine if data is fresh enough
        4. Apply cache strategy
        5. Return data with cache metadata

        Args:
            currency_pair: Trading pair (e.g., 'btcusd')
            timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
            candles: Number of candles needed
            strategy: Cache strategy to use

        Returns:
            Dict containing:
            - data: pd.DataFrame or None
            - cache_hit: bool
            - freshness_score: float (0-1)
            - needs_refresh: bool
            - cache_source: str ('memory', 'database', 'none')
        """
        cache_key = self._generate_cache_key(currency_pair, timeframe, candles)

        # Step 1: Check memory cache first
        memory_entry = await self._check_memory_cache(cache_key)
        if memory_entry and memory_entry.is_fresh():
            memory_entry.access()
            self._stats['cache_hits'] += 1
            self._stats['memory_hits'] += 1

            return {
                'data': memory_entry.data,
                'cache_hit': True,
                'freshness_score': 1.0,
                'needs_refresh': False,
                'cache_source': 'memory',
                'quality_score': memory_entry.quality_score
            }

        # Step 2: Check database cache
        database_data = await self._check_database_cache(currency_pair, timeframe, candles)
        if database_data is not None and not database_data.empty:
            # Assess freshness
            is_fresh, freshness_score = self.is_data_fresh(currency_pair, timeframe, datetime.utcnow())

            self._stats['cache_hits'] += 1
            self._stats['database_hits'] += 1

            # Cache in memory for future requests
            if is_fresh:
                await self._store_in_memory_cache(cache_key, database_data, freshness_score)

            return {
                'data': database_data,
                'cache_hit': True,
                'freshness_score': freshness_score,
                'needs_refresh': not is_fresh,
                'cache_source': 'database',
                'quality_score': database_data.get('quality_score', [0.8]).mean() if 'quality_score' in database_data.columns else 0.8
            }

        # Step 3: Cache miss
        self._stats['cache_misses'] += 1

        return {
            'data': None,
            'cache_hit': False,
            'freshness_score': 0.0,
            'needs_refresh': True,
            'cache_source': 'none',
            'quality_score': 0.0
        }

    async def cache_data(self, currency_pair: str, timeframe: str,
                        data: pd.DataFrame, quality_score: float = 1.0) -> bool:
        """
        Cache data in both memory and database layers.

        Caching process:
        1. Validate data quality
        2. Calculate cache expiry time
        3. Store in memory cache
        4. Store in unified database
        5. Update cache statistics

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            data: DataFrame to cache
            quality_score: Quality score for the data

        Returns:
            bool: True if caching was successful
        """
        try:
            if data is None or data.empty:
                logger.warning(f"Attempted to cache empty data for {currency_pair} {timeframe}")
                return False

            # Calculate TTL based on timeframe and quality
            ttl = self._calculate_cache_ttl(timeframe, quality_score)
            expiry_time = datetime.utcnow() + ttl

            # Store in memory cache
            cache_key = self._generate_cache_key(currency_pair, timeframe, len(data))
            await self._store_in_memory_cache(cache_key, data, quality_score, expiry_time)

            # TODO: Store in unified database (will implement when database layer is ready)
            # await self._store_in_database_cache(currency_pair, timeframe, data, quality_score)

            logger.debug(f"Cached {len(data)} records for {currency_pair} {timeframe} with TTL {ttl}")
            return True

        except Exception as e:
            logger.error(f"Failed to cache data for {currency_pair} {timeframe}: {str(e)}")
            return False

    def is_data_fresh(self, currency_pair: str, timeframe: str,
                     last_update: datetime) -> Tuple[bool, float]:
        """
        Check if cached data is still fresh.

        Freshness assessment:
        - Compare against timeframe-specific TTL
        - Consider market hours and volatility
        - Account for data quality
        - Factor in access patterns

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            last_update: When data was last updated

        Returns:
            Tuple of (is_fresh: bool, freshness_score: float)
        """
        try:
            # Get timeframe-specific expiry time
            expiry_minutes = CACHE_EXPIRY_MINUTES.get(timeframe, 60)  # Default 60 minutes
            max_age = timedelta(minutes=expiry_minutes)

            # Calculate age of data
            age = datetime.utcnow() - last_update

            # Calculate freshness score (1.0 = fresh, 0.0 = stale)
            if age <= max_age:
                # Fresh data - score based on how recent it is
                freshness_score = max(0.0, 1.0 - (age.total_seconds() / max_age.total_seconds()))
                return True, freshness_score
            else:
                # Stale data - score based on how stale it is
                staleness_factor = age.total_seconds() / max_age.total_seconds()
                freshness_score = max(0.0, 1.0 / staleness_factor)
                return False, freshness_score

        except Exception as e:
            logger.error(f"Error checking data freshness: {str(e)}")
            return False, 0.0

    async def warm_cache(self, currency_pairs: List[str], timeframes: List[str],
                        priority: str = 'normal') -> Dict[str, Any]:
        """
        Warm cache with commonly requested data.

        Cache warming strategies:
        - Preload popular currency pairs
        - Cache multiple timeframes
        - Prioritize high-quality sources
        - Schedule during low-traffic periods

        Args:
            currency_pairs: List of pairs to warm
            timeframes: List of timeframes to warm
            priority: Warming priority ('low', 'normal', 'high')

        Returns:
            Dict with warming results and statistics

        TODO: Implement cache warming:
        - Prioritize warming tasks
        - Fetch data from sources
        - Cache with appropriate TTL
        - Monitor warming performance
        """
        pass

    async def invalidate_cache(self, currency_pair: str = None,
                              timeframe: str = None, reason: str = 'manual') -> int:
        """
        Invalidate cached data based on criteria.

        Invalidation scenarios:
        - Manual invalidation
        - Data quality issues
        - Source reliability problems
        - System maintenance

        Args:
            currency_pair: Specific pair to invalidate (None for all)
            timeframe: Specific timeframe to invalidate (None for all)
            reason: Reason for invalidation

        Returns:
            int: Number of cache entries invalidated

        TODO: Implement cache invalidation:
        - Remove from memory cache
        - Mark database entries as stale
        - Log invalidation events
        - Update statistics
        """
        pass

    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive cache performance statistics.

        Statistics include:
        - Cache hit/miss ratios
        - Memory usage
        - Database cache size
        - Average response times
        - Background refresh rates
        - Quality distributions

        Returns:
            Dict with cache statistics
        """
        total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
        hit_ratio = self._stats['cache_hits'] / total_requests if total_requests > 0 else 0.0

        # Calculate quality statistics
        quality_scores = []
        for entry in self._memory_cache.values():
            quality_scores.append(entry.quality_score)

        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

        return {
            'cache_hits': self._stats['cache_hits'],
            'cache_misses': self._stats['cache_misses'],
            'hit_ratio': hit_ratio,
            'memory_hits': self._stats['memory_hits'],
            'database_hits': self._stats['database_hits'],
            'background_updates': self._stats['background_updates'],
            'cache_evictions': self._stats['cache_evictions'],
            'memory_cache_size': len(self._memory_cache),
            'max_memory_entries': self._max_memory_entries,
            'avg_quality_score': avg_quality,
            'active_background_tasks': len(self._background_tasks)
        }

    async def _check_memory_cache(self, cache_key: str) -> Optional[CacheEntry]:
        """
        Check memory cache for data.

        Args:
            cache_key: Cache key to lookup

        Returns:
            CacheEntry if found, None otherwise
        """
        if cache_key in self._memory_cache:
            entry = self._memory_cache[cache_key]
            if entry.is_fresh():
                return entry
            else:
                # Remove expired entry
                del self._memory_cache[cache_key]
                self._stats['cache_evictions'] += 1

        return None

    async def _check_database_cache(self, currency_pair: str, timeframe: str,
                                   candles: int) -> Optional[pd.DataFrame]:
        """
        Check unified database cache for data.

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            candles: Number of candles needed

        Returns:
            DataFrame if found, None otherwise
        """
        # TODO: Implement database cache lookup when unified database is ready
        # For now, return None (cache miss)
        return None

    def _calculate_cache_ttl(self, timeframe: str, quality_score: float) -> timedelta:
        """
        Calculate appropriate TTL for cached data.

        TTL factors:
        - Timeframe (shorter timeframes expire faster)
        - Data quality (higher quality cached longer)
        - Market volatility
        - Access patterns

        Args:
            timeframe: Time interval
            quality_score: Quality score of the data

        Returns:
            TTL as timedelta
        """
        # Base TTL from configuration
        base_minutes = CACHE_EXPIRY_MINUTES.get(timeframe, 60)

        # Quality bonus: high quality data gets longer TTL
        quality_bonus = quality_score * 0.2  # Up to 20% bonus

        # Calculate final TTL
        final_minutes = base_minutes * (1 + quality_bonus)

        return timedelta(minutes=final_minutes)

    async def _background_refresh_task(self, currency_pair: str, timeframe: str,
                                      missing_timestamps: List[int]) -> None:
        """
        Background task to refresh stale cache data.

        Args:
            currency_pair: Trading pair to refresh
            timeframe: Time interval
            missing_timestamps: Timestamps that need refreshing

        TODO: Implement background refresh:
        - Queue refresh tasks
        - Coordinate with SourceCoordinator
        - Update cache with fresh data
        - Handle refresh failures
        """
        pass

    def _generate_cache_key(self, currency_pair: str, timeframe: str,
                           candles: int) -> str:
        """
        Generate cache key for data lookup.

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            candles: Number of candles

        Returns:
            Cache key string
        """
        return f"{currency_pair}:{timeframe}:{candles}"

    async def _store_in_memory_cache(self, cache_key: str, data: pd.DataFrame,
                                    quality_score: float, expiry_time: datetime = None) -> None:
        """
        Store data in memory cache.

        Args:
            cache_key: Cache key
            data: DataFrame to cache
            quality_score: Quality score
            expiry_time: When cache expires
        """
        if expiry_time is None:
            # Calculate default expiry
            expiry_time = datetime.utcnow() + timedelta(minutes=60)

        # Check if we need to evict old entries
        if len(self._memory_cache) >= self._max_memory_entries:
            await self._evict_old_entries()

        # Create cache entry
        entry = CacheEntry(
            data=data.copy(),
            timestamp=datetime.utcnow(),
            quality_score=quality_score,
            source_count=1,  # Will be updated when we have multi-source data
            expiry_time=expiry_time
        )

        self._memory_cache[cache_key] = entry
        logger.debug(f"Stored {len(data)} records in memory cache with key {cache_key}")

    async def _evict_old_entries(self) -> None:
        """Evict old entries from memory cache to make space."""
        # Simple LRU eviction - remove oldest accessed entries
        if not self._memory_cache:
            return

        # Sort by last accessed time and remove oldest 10%
        sorted_entries = sorted(
            self._memory_cache.items(),
            key=lambda x: x[1].last_accessed
        )

        evict_count = max(1, len(sorted_entries) // 10)
        for i in range(evict_count):
            key, _ = sorted_entries[i]
            del self._memory_cache[key]
            self._stats['cache_evictions'] += 1

        logger.debug(f"Evicted {evict_count} entries from memory cache")
