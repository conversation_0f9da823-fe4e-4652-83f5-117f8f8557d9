"""
Unified Database Package - Database operations for unified data storage

This package contains all database operations related to the unified data storage
system. It provides a centralized interface for storing and retrieving aggregated
data from multiple sources.

Modules:
- store_unified_data.py: Store aggregated data in unified database
- get_unified_data.py: Retrieve data from unified database
- merge_source_data.py: Merge data from multiple sources intelligently
- unified_schema.py: Database schema definitions for unified storage

Key Concepts:
1. Unified Storage: Single source of truth for all aggregated data
2. Source Tracking: Maintain information about data sources
3. Quality Metadata: Store quality scores and metrics with data
4. Efficient Retrieval: Optimized queries for endpoint consumption
5. Data Integrity: Ensure consistency and reliability

Database Schema:
The unified database uses an enhanced schema that includes:
- Standard OHLC data (timestamp, open, high, low, close, volume)
- Source metadata (contributing sources, primary source)
- Quality information (quality scores, validation flags)
- Aggregation metadata (aggregation method, timestamp)

Table Structure:
```sql
CREATE TABLE unified_ohlc (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    currency_pair TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    timestamp BIGINT NOT NULL,
    date DATETIME NOT NULL,
    open FLOAT NOT NULL,
    high FLOAT NOT NULL,
    low FLOAT NOT NULL,
    close FLOAT NOT NULL,
    volume FLOAT NOT NULL,
    source_count INTEGER DEFAULT 1,
    primary_source TEXT,
    contributing_sources TEXT,  -- JSON array of sources
    quality_score FLOAT DEFAULT 1.0,
    aggregation_method TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(currency_pair, timeframe, timestamp)
);
```

Indexes for Performance:
- PRIMARY KEY on id
- UNIQUE INDEX on (currency_pair, timeframe, timestamp)
- INDEX on (currency_pair, timeframe, date) for range queries
- INDEX on quality_score for quality-based filtering
- INDEX on updated_at for freshness queries

Performance Optimizations:
- Efficient indexing strategy
- Batch insert operations
- Connection pooling
- Query optimization
- Memory-efficient data handling

Data Integrity:
- Foreign key constraints where applicable
- Check constraints for data validation
- Transaction management
- Backup and recovery procedures
"""

# Database configuration constants
UNIFIED_DB_NAME = "unified_ohlc.db"
UNIFIED_TABLE_PREFIX = "unified_"

# Schema version for migration management
SCHEMA_VERSION = "1.0.0"

# Default quality thresholds for unified data
UNIFIED_QUALITY_THRESHOLDS = {
    'minimum_quality_score': 0.7,
    'minimum_source_count': 1,
    'maximum_age_hours': 24
}

# Supported aggregation methods
AGGREGATION_METHODS = [
    'quality_weighted',
    'volume_weighted',
    'simple_average',
    'best_source'
]

# Source tracking configuration
SOURCE_TRACKING = {
    'track_contributing_sources': True,
    'track_primary_source': True,
    'track_source_weights': True,
    'track_quality_metrics': True
}
