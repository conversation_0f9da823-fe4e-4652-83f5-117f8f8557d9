"""
Unified Schema - Database schema definitions for unified data storage

This module defines the database schema for the unified data storage system.
It provides schema creation, migration, and validation functionality.

Key Responsibilities:
1. Define unified database schema
2. Create database tables and indexes
3. Handle schema migrations
4. Validate schema compliance
5. Optimize database performance
6. Manage schema versioning

Schema Design:
- Unified OHLC table with source tracking
- Quality metadata storage
- Performance-optimized indexes
- Referential integrity constraints
- Audit trail capabilities

Table Structure:
- Primary data table: unified_ohlc
- Metadata tables: source_metadata, quality_metrics
- System tables: schema_version, migration_log
- Performance tables: query_cache, statistics

Performance Features:
- Composite indexes for common queries
- Partitioning for large datasets
- Query optimization hints
- Connection pooling support
- Efficient data types

Data Integrity:
- Primary key constraints
- Foreign key relationships
- Check constraints for data validation
- Unique constraints for deduplication
- Trigger-based audit trails
"""

import sqlite3
import os
from typing import Dict, List, Optional, Any
from app.logger.get_logger import log, logger


# Schema version for migration management
CURRENT_SCHEMA_VERSION = "1.0.0"

# SQL for creating the main unified OHLC table
CREATE_UNIFIED_OHLC_TABLE = """
CREATE TABLE IF NOT EXISTS unified_ohlc (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    currency_pair TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    timestamp BIGINT NOT NULL,
    date DATETIME NOT NULL,
    open FLOAT NOT NULL CHECK (open > 0),
    high FLOAT NOT NULL CHECK (high > 0),
    low FLOAT NOT NULL CHECK (low > 0),
    close FLOAT NOT NULL CHECK (close > 0),
    volume FLOAT NOT NULL CHECK (volume >= 0),
    source_count INTEGER DEFAULT 1 CHECK (source_count > 0),
    primary_source TEXT,
    contributing_sources TEXT,  -- JSON array of contributing sources
    source_weights TEXT,        -- JSON object with source weights
    quality_score FLOAT DEFAULT 1.0 CHECK (quality_score >= 0 AND quality_score <= 1),
    aggregation_method TEXT DEFAULT 'single_source',
    gap_filled BOOLEAN DEFAULT FALSE,
    interpolated BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    -- Ensure OHLC relationships are valid
    CHECK (high >= open AND high >= close),
    CHECK (low <= open AND low <= close),
    CHECK (high >= low),

    -- Unique constraint for currency_pair, timeframe, timestamp combination
    UNIQUE(currency_pair, timeframe, timestamp)
);
"""

# Performance indexes for the unified OHLC table
CREATE_UNIFIED_INDEXES = [
    "CREATE INDEX IF NOT EXISTS idx_unified_currency_timeframe ON unified_ohlc(currency_pair, timeframe);",
    "CREATE INDEX IF NOT EXISTS idx_unified_timestamp ON unified_ohlc(timestamp);",
    "CREATE INDEX IF NOT EXISTS idx_unified_date_range ON unified_ohlc(currency_pair, timeframe, date);",
    "CREATE INDEX IF NOT EXISTS idx_unified_quality ON unified_ohlc(quality_score);",
    "CREATE INDEX IF NOT EXISTS idx_unified_updated ON unified_ohlc(updated_at);",
    "CREATE INDEX IF NOT EXISTS idx_unified_source ON unified_ohlc(primary_source);",
    "CREATE INDEX IF NOT EXISTS idx_unified_composite ON unified_ohlc(currency_pair, timeframe, timestamp, quality_score);"
]

# SQL for creating source metadata table
CREATE_SOURCE_METADATA_TABLE = """
CREATE TABLE IF NOT EXISTS source_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_name TEXT NOT NULL,
    currency_pair TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    last_fetch_timestamp BIGINT,
    last_fetch_date DATETIME,
    total_records INTEGER DEFAULT 0,
    successful_fetches INTEGER DEFAULT 0,
    failed_fetches INTEGER DEFAULT 0,
    average_quality_score FLOAT DEFAULT 1.0,
    reliability_score FLOAT DEFAULT 1.0,
    last_error TEXT,
    last_error_timestamp BIGINT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(source_name, currency_pair, timeframe)
);
"""

# SQL for creating quality metrics table
CREATE_QUALITY_METRICS_TABLE = """
CREATE TABLE IF NOT EXISTS quality_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    currency_pair TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    timestamp BIGINT NOT NULL,
    completeness_score FLOAT,
    accuracy_score FLOAT,
    consistency_score FLOAT,
    timeliness_score FLOAT,
    validity_score FLOAT,
    overall_quality_score FLOAT,
    source_agreement_score FLOAT,
    outlier_count INTEGER DEFAULT 0,
    gap_filled_count INTEGER DEFAULT 0,
    interpolated_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (currency_pair, timeframe, timestamp)
    REFERENCES unified_ohlc (currency_pair, timeframe, timestamp)
);
"""

# SQL for creating schema version table
CREATE_SCHEMA_VERSION_TABLE = """
CREATE TABLE IF NOT EXISTS schema_version (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);
"""

# SQL for creating migration log table
CREATE_MIGRATION_LOG_TABLE = """
CREATE TABLE IF NOT EXISTS migration_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    migration_name TEXT NOT NULL,
    from_version TEXT,
    to_version TEXT,
    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    execution_time_ms INTEGER
);
"""


@log
def create_unified_schema(db_path: str) -> bool:
    """
    Create the complete unified database schema.

    Creates all tables, indexes, and constraints needed for
    the unified data storage system.

    Args:
        db_path: Path to the database file

    Returns:
        bool: True if schema creation was successful
    """
    try:
        # Ensure directory exists
        if db_path:
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
        else:
            logger.error("Database path is empty")
            return False

        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Create all tables
        tables = [
            CREATE_UNIFIED_OHLC_TABLE,
            CREATE_SOURCE_METADATA_TABLE,
            CREATE_QUALITY_METRICS_TABLE,
            CREATE_SCHEMA_VERSION_TABLE,
            CREATE_MIGRATION_LOG_TABLE
        ]

        for table_sql in tables:
            cursor.execute(table_sql)

        # Create indexes
        for index_sql in CREATE_UNIFIED_INDEXES:
            cursor.execute(index_sql)

        # Insert schema version
        cursor.execute(
            "INSERT OR IGNORE INTO schema_version (version, description) VALUES (?, ?)",
            (CURRENT_SCHEMA_VERSION, "Initial unified database schema")
        )

        # Commit changes
        conn.commit()
        conn.close()

        logger.info(f"Successfully created unified schema at {db_path}")
        return True

    except Exception as e:
        logger.error(f"Error creating unified schema: {str(e)}")
        return False


@log
def validate_schema_compliance(db_path: str) -> Dict[str, Any]:
    """
    Validate that database schema complies with unified schema.

    Validation checks:
    - All required tables exist
    - Correct column definitions
    - Proper indexes are present
    - Constraints are enforced
    - Schema version is current

    Args:
        db_path: Path to database to validate

    Returns:
        Dict with validation results and any issues found
    """
    try:
        if not os.path.exists(db_path):
            return {"valid": False, "error": "Database file does not exist"}

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        validation_results = {
            "valid": True,
            "issues": [],
            "tables": {},
            "indexes": {},
            "schema_version": None
        }

        # Check required tables
        required_tables = ['unified_ohlc', 'source_metadata', 'quality_metrics', 'schema_version']

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]

        for table in required_tables:
            if table not in existing_tables:
                validation_results["issues"].append(f"Missing required table: {table}")
                validation_results["valid"] = False
            else:
                validation_results["tables"][table] = "exists"

        # Check unified_ohlc table structure
        if 'unified_ohlc' in existing_tables:
            cursor.execute("PRAGMA table_info(unified_ohlc)")
            columns = {row[1]: row[2] for row in cursor.fetchall()}

            required_columns = {
                'currency_pair': 'TEXT',
                'timeframe': 'TEXT',
                'timestamp': 'BIGINT',
                'open': 'FLOAT',
                'high': 'FLOAT',
                'low': 'FLOAT',
                'close': 'FLOAT',
                'volume': 'FLOAT',
                'quality_score': 'FLOAT'
            }

            for col, expected_type in required_columns.items():
                if col not in columns:
                    validation_results["issues"].append(f"Missing column in unified_ohlc: {col}")
                    validation_results["valid"] = False

        # Check schema version
        try:
            cursor.execute("SELECT version FROM schema_version ORDER BY applied_at DESC LIMIT 1")
            version = cursor.fetchone()
            if version:
                validation_results["schema_version"] = version[0]
                if version[0] != CURRENT_SCHEMA_VERSION:
                    validation_results["issues"].append(f"Schema version mismatch: {version[0]} != {CURRENT_SCHEMA_VERSION}")
            else:
                validation_results["issues"].append("No schema version found")
                validation_results["valid"] = False
        except:
            validation_results["issues"].append("Cannot read schema version")
            validation_results["valid"] = False

        conn.close()
        return validation_results

    except Exception as e:
        logger.error(f"Error validating schema: {str(e)}")
        return {"valid": False, "error": str(e)}


def get_current_schema_version(db_path: str) -> Optional[str]:
    """
    Get the current schema version from the database.

    Args:
        db_path: Path to the database file

    Returns:
        Current schema version string or None if not found
    """
    try:
        if not os.path.exists(db_path):
            return None

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT version FROM schema_version ORDER BY applied_at DESC LIMIT 1")
        result = cursor.fetchone()

        conn.close()

        return result[0] if result else None

    except Exception as e:
        logger.error(f"Error getting schema version: {str(e)}")
        return None


def migrate_schema(db_path: str, target_version: str = None) -> bool:
    """
    Migrate database schema to target version.

    Migration process:
    - Determine current version
    - Plan migration path
    - Execute migration steps
    - Validate results
    - Update version tracking

    Args:
        db_path: Path to database to migrate
        target_version: Target schema version (latest if None)

    Returns:
        bool: True if migration was successful

    TODO: Implement schema migration:
    - Determine migration path
    - Execute migration steps
    - Handle rollback on failure
    - Update version tracking
    - Log migration results
    """
    pass


def _execute_sql_script(db_path: str, sql_statements: List[str]) -> bool:
    """
    Execute a list of SQL statements safely.

    Args:
        db_path: Path to database
        sql_statements: List of SQL statements to execute

    Returns:
        bool: True if all statements executed successfully

    TODO: Implement SQL execution:
    - Execute statements in transaction
    - Handle errors gracefully
    - Rollback on failure
    - Log execution results
    """
    pass


def _create_performance_indexes(db_path: str) -> bool:
    """
    Create performance-optimized indexes.

    Args:
        db_path: Path to database

    Returns:
        bool: True if indexes created successfully

    TODO: Implement index creation:
    - Create all performance indexes
    - Handle existing indexes
    - Optimize for common queries
    - Log creation results
    """
    pass


def optimize_database_performance(db_path: str) -> Dict[str, Any]:
    """
    Optimize database performance settings and structure.

    Optimization operations:
    - Analyze table statistics
    - Rebuild indexes if needed
    - Optimize query plans
    - Configure performance settings

    Args:
        db_path: Path to database to optimize

    Returns:
        Dict with optimization results

    TODO: Implement performance optimization:
    - Analyze table statistics
    - Rebuild fragmented indexes
    - Update query plans
    - Configure SQLite settings
    """
    pass


def get_schema_statistics(db_path: str) -> Dict[str, Any]:
    """
    Get comprehensive statistics about the database schema.

    Statistics include:
    - Table sizes and row counts
    - Index usage statistics
    - Storage efficiency metrics
    - Performance indicators

    Args:
        db_path: Path to database

    Returns:
        Dict with schema statistics

    TODO: Implement statistics collection:
    - Query table sizes
    - Analyze index usage
    - Calculate storage efficiency
    - Measure performance metrics
    """
    pass
