import os

from sqlalchemy import exc
from sqlalchemy import create_engine
import pandas as pd
from app.logger.get_logger import log


@log
def store_in_db(db_name: str, table_name: str, df: pd.DataFrame, exchange: str):
    """
    Stores a DataFrame in a SQLite database.

    Args:
        db_name (str): The name of the SQLite database.
        table_name (str): The name of the table in the database.
        df (pd.DataFrame): The DataFrame to be stored.
        exchange (str): The name of the exchange (used as subfolder).

    Returns:
        None
    """
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', exchange, db_name)

    # Ensure the exchange directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    engine = create_engine(f'sqlite:///{db_path}')

    existing_df = _read_from_db(engine, table_name)
    df = pd.concat([existing_df, df])

    # Sort and remove duplicates
    df = df.sort_values(by = 'timestamp').drop_duplicates(subset = 'timestamp', keep = 'last')

    _write_to_db(engine, table_name, df, existing_df)

    return


@log
def _read_from_db(engine, table_name):
    """
    Reads existing table from database.

    If the table does not exist, skips reading and proceeds with writing.

    Parameters
    ----------
    engine : sqlalchemy engine
        to access the database
    table_name : str
        Name of the table to read from

    Returns
    -------
    pd.DataFrame
        Existing data in the table
    """
    try:
        # Read existing table from database
        existing_df = pd.read_sql_query(f"SELECT * FROM {table_name}", engine, parse_dates = ['date'])
    except exc.OperationalError:
        # If table does not exist, skip reading and proceed with writing
        existing_df = pd.DataFrame()
    return existing_df


@log
def _write_to_db(engine, table_name, df, existing_df):
    """
    Writes data to a database table. If the table does not exist, it creates a new one.
    If the table exists, it appends new data that does not have a matching timestamp
    in the existing data.

    Args:
        engine: SQLAlchemy engine connected to the database.
        table_name (str): The name of the table in the database.
        df (pd.DataFrame): DataFrame containing the data to be written to the database.
        existing_df (pd.DataFrame): DataFrame containing the existing data from the database table.
    """
    if existing_df.empty:
        # Set column types
        df['timestamp'] = df['timestamp'].astype('int64')
        df['date'] = pd.to_datetime(df['date'])

        # Table does not exist, create a new one
        df.to_sql(table_name, engine, if_exists = 'fail', index = False)
    else:
        # Table already exists, append new data to it
        temp_df = df[~df['timestamp'].isin(existing_df['timestamp'])].copy()
        # Set column types for the new data
        temp_df['timestamp'] = temp_df['timestamp'].astype('int64')
        temp_df['date'] = pd.to_datetime(temp_df['date'])
        # Append new data to the existing table
        temp_df.to_sql(table_name, engine, if_exists = 'append', index = False)
