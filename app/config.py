# Config file
EXCLUDE_CURRENT_CANDLE_BITSTAMP = True

# Logging
LOG_LEVEL = 'DEBUG'

# Sources configuration
SOURCES = ['bitstamp', 'coindesk']  # Easy to add new sources like 'kraken'

# Data aggregation settings
AGGREGATION_STRATEGY = 'quality_weighted'  # or 'volume_weighted', 'simple_average', 'best_source'
MIN_SOURCES_REQUIRED = 1  # Minimum sources needed for valid data
DATA_QUALITY_THRESHOLD = 0.7  # Minimum quality score (0-1)

# Source priorities and reliability scores (higher = better, 0-1 scale)
SOURCE_PRIORITIES = {
    'bitstamp': 0.90,   # High reliability, good API
    'coindesk': 0.85,   # Good reliability
    'kraken': 0.88      # Future: High reliability when implemented
}

# Source-specific timeouts (seconds)
SOURCE_TIMEOUTS = {
    'bitstamp': 30,
    'coindesk': 45,
    'kraken': 30
}

# Cache and freshness settings
CACHE_EXPIRY_MINUTES = {
    'm1': 2,    # 1-minute data expires after 2 minutes
    'm5': 6,    # 5-minute data expires after 6 minutes
    'm15': 16,  # 15-minute data expires after 16 minutes
    'h1': 61,   # 1-hour data expires after 61 minutes
    'h4': 241,  # 4-hour data expires after 241 minutes
    'd1': 1441  # Daily data expires after 1441 minutes (24+ hours)
}

# Cache strategy settings
CACHE_STRATEGY = 'cache_first'  # 'cache_only', 'cache_first', 'fresh_first', 'background'
CACHE_HIT_THRESHOLD = 0.8  # Minimum cache hit ratio to consider cache healthy
MEMORY_CACHE_SIZE = 1000   # Maximum entries in memory cache
CACHE_WARMING_ENABLED = True  # Enable automatic cache warming

# Cache freshness rules
FRESHNESS_RULES = {
    'm1': {
        'max_age_minutes': 2,
        'quality_bonus_minutes': 1,    # Extra time for high-quality data
        'market_hours_factor': 1.5     # Longer TTL during off-hours
    },
    'm5': {
        'max_age_minutes': 6,
        'quality_bonus_minutes': 2,
        'market_hours_factor': 1.5
    },
    'm15': {
        'max_age_minutes': 16,
        'quality_bonus_minutes': 4,
        'market_hours_factor': 1.3
    },
    'h1': {
        'max_age_minutes': 61,
        'quality_bonus_minutes': 15,
        'market_hours_factor': 1.2
    },
    'h4': {
        'max_age_minutes': 241,
        'quality_bonus_minutes': 60,
        'market_hours_factor': 1.1
    },
    'd1': {
        'max_age_minutes': 1441,
        'quality_bonus_minutes': 240,
        'market_hours_factor': 1.0
    }
}

# Quality assessment thresholds
QUALITY_THRESHOLDS = {
    'completeness': 0.95,      # 95% of expected data points
    'consistency': 0.90,       # 90% agreement between sources
    'timeliness': 300,         # Data not older than 5 minutes (seconds)
    'volume_correlation': 0.80, # 80% volume correlation between sources
    'price_reasonableness': 0.05 # 5% maximum price deviation tolerance
}

# Database settings
UNIFIED_DB_ENABLED = True  # Enable unified database functionality
UNIFIED_DB_PATH = 'app/db/data/unified/'
SOURCE_DB_PATH = 'app/db/data/'  # Keep source-specific databases for backup

# Performance settings
MAX_CONCURRENT_SOURCES = 3  # Maximum concurrent source requests
BATCH_SIZE = 1000  # Batch size for database operations
CONNECTION_POOL_SIZE = 5  # Database connection pool size

# Circuit breaker settings for source health
CIRCUIT_BREAKER = {
    'failure_threshold': 5,    # Failures before opening circuit
    'recovery_timeout': 300,   # Seconds before attempting recovery
    'success_threshold': 3     # Successes needed to close circuit
}

# Background task settings
BACKGROUND_TASKS = {
    'enabled': True,
    'max_concurrent_updates': 3,      # Max concurrent background updates
    'update_queue_size': 100,         # Max queued background tasks
    'retry_attempts': 3,              # Retry failed background updates
    'retry_delay_seconds': 60,        # Delay between retries
    'cleanup_interval_hours': 6,      # How often to clean old cache data
    'warming_schedule': {             # Cache warming schedule
        'enabled': True,
        'interval_minutes': 30,       # How often to warm cache
        'popular_pairs': ['btcusd', 'ethusd'],  # Pairs to keep warm
        'timeframes': ['m1', 'm5', 'h1']        # Timeframes to warm
    }
}

# Other constants
# API_KEY = 'your_api_key_here'
# DB_HOST = 'localhost'
# DB_PORT = 5432