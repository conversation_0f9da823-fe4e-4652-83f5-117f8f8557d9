"""
Kraken Candles Data Fetcher

This module fetches OHLC candle data from the Kraken API following the same pattern
as other source handlers in the project.

Kraken API Details:
- Endpoint: GET /0/public/OHLC
- Rate Limits: Varies by endpoint and tier
- Data Format: Array of arrays with OHLC data
- Timeframes: 1, 5, 15, 30, 60, 240, 1440, 10080, 21600 (minutes)
"""

import json
import pandas as pd
from typing import Optional
from datetime import datetime

from app.sources.kraken.api.spot.market_data.get_ohlc_data import request
from app.logger.get_logger import log, logger


# Kraken timeframe mapping (minutes)
KRAKEN_TIMEFRAMES = {
    'm1': 1,
    'm5': 5,
    'm15': 15,
    'm30': 30,
    'h1': 60,
    'h4': 240,
    'd1': 1440,
    'w1': 10080,
    'M1': 21600
}

# Kraken currency pair mapping
KRAKEN_PAIRS = {
    'btcusd': 'XBTUSD',
    'ethusd': 'ETHUSD',
    'ethbtc': 'ETHXBT',
    'ltcusd': 'LTCUSD',
    'adausd': 'ADAUSD',
    'dotusd': 'DOTUSD'
}


@log
def get_candles_from_kraken(currency_pair: str, timeframe: str, candles: int, ecc: bool = True) -> Optional[pd.DataFrame]:
    """
    Fetch OHLC candle data from Kraken API.
    
    Args:
        currency_pair: Trading pair (e.g., 'btcusd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        candles: Number of candles to fetch (max 720 for Kraken)
        ecc: Exclude current candle (default: True)
        
    Returns:
        DataFrame with OHLC data or None if error
    """
    try:
        # Validate inputs
        if timeframe not in KRAKEN_TIMEFRAMES:
            logger.error(f"Unsupported timeframe for Kraken: {timeframe}")
            return None
            
        if currency_pair not in KRAKEN_PAIRS:
            logger.error(f"Unsupported currency pair for Kraken: {currency_pair}")
            return None
            
        # Kraken limits to 720 candles
        if candles > 720:
            logger.warning(f"Kraken limits to 720 candles, requested {candles}. Using 720.")
            candles = 720
            
        # Prepare API request
        kraken_pair = KRAKEN_PAIRS[currency_pair]
        kraken_interval = KRAKEN_TIMEFRAMES[timeframe]
        
        logger.info(f"Fetching {candles} {timeframe} candles for {currency_pair} from Kraken")
        
        # Make API request
        response = request(
            method="GET",
            path="/0/public/OHLC",
            query={
                "pair": kraken_pair,
                "interval": kraken_interval
            },
            environment="https://api.kraken.com"
        )
        
        # Parse response
        response_data = response.read().decode()
        data = json.loads(response_data)
        
        # Check for errors
        if 'error' in data and data['error']:
            logger.error(f"Kraken API error: {data['error']}")
            return None
            
        if 'result' not in data:
            logger.error("No result in Kraken API response")
            return None
            
        # Extract OHLC data
        result = data['result']
        
        # Find the pair data (Kraken returns pair name as key)
        ohlc_data = None
        for key, value in result.items():
            if key != 'last' and isinstance(value, list):
                ohlc_data = value
                break
                
        if not ohlc_data:
            logger.error("No OHLC data found in Kraken response")
            return None
            
        # Convert to DataFrame
        df = _parse_kraken_ohlc_data(ohlc_data, ecc)
        
        # Limit to requested number of candles
        if len(df) > candles:
            df = df.tail(candles)
            
        logger.info(f"Successfully fetched {len(df)} candles from Kraken")
        return df
        
    except Exception as e:
        logger.error(f"Error fetching data from Kraken: {str(e)}")
        return None


def _parse_kraken_ohlc_data(ohlc_data: list, ecc: bool = True) -> pd.DataFrame:
    """
    Parse Kraken OHLC array data into DataFrame.
    
    Kraken OHLC format: [time, open, high, low, close, vwap, volume, count]
    
    Args:
        ohlc_data: List of OHLC arrays from Kraken
        ecc: Exclude current candle
        
    Returns:
        DataFrame with standardized OHLC format
    """
    try:
        # Convert to DataFrame
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'vwap', 'volume', 'count']
        df = pd.DataFrame(ohlc_data, columns=columns)
        
        # Exclude current candle if requested (last entry is current/incomplete)
        if ecc and len(df) > 0:
            df = df[:-1]
            
        # Convert data types
        df['timestamp'] = pd.to_numeric(df['timestamp'], errors='coerce').astype('int64')
        df['open'] = pd.to_numeric(df['open'], errors='coerce').astype('float64')
        df['high'] = pd.to_numeric(df['high'], errors='coerce').astype('float64')
        df['low'] = pd.to_numeric(df['low'], errors='coerce').astype('float64')
        df['close'] = pd.to_numeric(df['close'], errors='coerce').astype('float64')
        df['volume'] = pd.to_numeric(df['volume'], errors='coerce').astype('float64')
        
        # Create datetime column
        df['date'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
        
        # Select only required columns (matching other sources)
        df = df[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']]
        
        # Remove any rows with NaN values
        df = df.dropna()
        
        # Sort by timestamp (ascending)
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        return df
        
    except Exception as e:
        logger.error(f"Error parsing Kraken OHLC data: {str(e)}")
        return pd.DataFrame()


def get_supported_pairs() -> list:
    """Get list of supported currency pairs for Kraken."""
    return list(KRAKEN_PAIRS.keys())


def get_supported_timeframes() -> list:
    """Get list of supported timeframes for Kraken."""
    return list(KRAKEN_TIMEFRAMES.keys())


def test_kraken_connection() -> bool:
    """Test connection to Kraken API."""
    try:
        response = request(
            method="GET",
            path="/0/public/Time",
            environment="https://api.kraken.com"
        )
        data = json.loads(response.read().decode())
        return 'error' not in data or not data['error']
    except Exception as e:
        logger.error(f"Kraken connection test failed: {str(e)}")
        return False


if __name__ == "__main__":
    # Test the function
    print("Testing Kraken API connection...")
    if test_kraken_connection():
        print("✓ Kraken API connection successful")
        
        print("\nTesting data fetch...")
        data = get_candles_from_kraken('btcusd', 'h1', 10)
        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} candles")
            print(data.head())
        else:
            print("✗ Failed to fetch data")
    else:
        print("✗ Kraken API connection failed")
