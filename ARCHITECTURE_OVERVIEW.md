# Dabot-OHLC Enhanced Architecture Overview

## Project Structure for Unified Data Aggregation

This document outlines the enhanced architecture that enables endpoints to query data from all sources through a unified aggregation system.

## Current vs Enhanced Architecture

### Current Flow
```
Endpoint → refresh_db_by_candles → [Source APIs] → store_in_db → get_from_db (single source)
```

### Cache-Optimized Flow
```
Endpoint → UnifiedDataManager → CacheManager → Check Unified Cache
    ↓                                              ↓
    ↓                                         Cache Hit: Return Data
    ↓                                              ↓
Cache Miss/Stale → SourceCoordinator → [All Sources] → Background Update
    ↓
Return Best Available Data (cache + fresh) → Update Cache
```

### Cache Strategy Benefits
- **Sub-second response times** for cached data
- **Background updates** don't block user requests
- **Intelligent cache warming** for popular data
- **Graceful degradation** when sources are slow/unavailable
- **Quality-aware caching** - better data cached longer

## Directory Structure

```
app/
├── core/
│   ├── unified_data_manager.py          # Central coordinator for all data operations
│   ├── source_coordinator.py            # Manages parallel data fetching from sources
│   ├── cache_manager.py                 # Intelligent caching system for unified data
│   ├── aggregators/                     # Data aggregation components
│   │   ├── __init__.py                  # Package configuration and constants
│   │   ├── base_aggregator.py           # Abstract base class for aggregators
│   │   ├── candles_aggregator.py        # OHLC candle data aggregation
│   │   ├── range_aggregator.py          # Date range data aggregation
│   │   └── data_quality_checker.py      # Comprehensive quality assessment
│   ├── translators/                     # Source-specific data translators
│   │   ├── __init__.py                  # Unified schema definitions
│   │   ├── base_translator.py           # Abstract base translator
│   │   ├── bitstamp_translator.py       # Bitstamp data translation
│   │   ├── coindesk_translator.py       # Coindesk data translation
│   │   └── kraken_translator.py         # Kraken data translation (future)
│   └── helpers/                         # Existing helper functions
├── db/
│   ├── unified/                         # Unified database operations
│   │   ├── __init__.py                  # Package configuration
│   │   ├── store_unified_data.py        # Store aggregated data
│   │   ├── get_unified_data.py          # Retrieve unified data
│   │   ├── merge_source_data.py         # Intelligent data merging
│   │   └── unified_schema.py            # Database schema management
│   ├── data/
│   │   ├── unified/                     # Unified database storage
│   │   │   └── *.db                     # Combined data from all sources
│   │   └── sources/                     # Source-specific databases (backup)
│   │       ├── bitstamp/
│   │       ├── coindesk/
│   │       └── kraken/
│   └── sqlite/                          # Existing source-specific operations
└── config.py                           # Enhanced configuration
```

## Key Components

### 1. UnifiedDataManager (`app/core/unified_data_manager.py`)
- **Purpose**: Central coordinator for all data operations
- **Responsibilities**:
  - Handle requests from API endpoints
  - Coordinate with SourceCoordinator for data fetching
  - Manage data freshness validation
  - Orchestrate aggregation and storage
  - Provide fallback mechanisms

### 2. SourceCoordinator (`app/core/source_coordinator.py`)
- **Purpose**: Manage parallel data fetching from multiple sources
- **Responsibilities**:
  - Coordinate concurrent source requests
  - Handle source failures gracefully
  - Implement retry logic and rate limiting
  - Route data through translators

### 3. Aggregators (`app/core/aggregators/`)
- **Purpose**: Combine data from multiple sources intelligently
- **Components**:
  - `base_aggregator.py`: Common aggregation interface
  - `candles_aggregator.py`: OHLC-specific aggregation logic
  - `range_aggregator.py`: Date range optimization
  - `data_quality_checker.py`: Comprehensive quality assessment

### 4. Translators (`app/core/translators/`)
- **Purpose**: Convert source-specific formats to unified format
- **Components**:
  - `base_translator.py`: Common translation interface
  - `bitstamp_translator.py`: Bitstamp data conversion
  - `coindesk_translator.py`: Coindesk data conversion
  - `kraken_translator.py`: Future Kraken support

### 5. Unified Database (`app/db/unified/`)
- **Purpose**: Single source of truth for aggregated data
- **Components**:
  - `store_unified_data.py`: Efficient data storage
  - `get_unified_data.py`: Optimized data retrieval
  - `merge_source_data.py`: Intelligent data merging
  - `unified_schema.py`: Database schema management

## Cache-Optimized Data Flow

### 1. Endpoint Request
```python
# Endpoint calls UnifiedDataManager
data = await unified_manager.get_candles('btcusd', 'h1', 24)
```

### 2. Cache Check
```python
# Check cache coverage and freshness
cache_result = await cache_manager.get_cached_data('btcusd', 'h1', 24)

if cache_result['cache_hit_ratio'] >= 0.8:
    # Sufficient cache coverage - return immediately
    return format_response(cache_result['data'])
```

### 3. Partial Cache Hit
```python
# Some data in cache, some missing/stale
if cache_result['cache_hit_ratio'] > 0.3:
    # Return cached data immediately
    response = format_response(cache_result['data'])

    # Trigger background update for missing/stale data
    asyncio.create_task(background_update(missing_timestamps))

    return response
```

### 4. Cache Miss - Fetch Fresh Data
```python
# Little/no cache data - fetch synchronously
source_data = await source_coordinator.fetch_from_all_sources()

# Translate and aggregate
translated_data = translate_all_sources(source_data)
aggregated_data = aggregate_data(translated_data)

# Update cache
await cache_manager.cache_data('btcusd', 'h1', aggregated_data)

return format_response(aggregated_data)
```

### 5. Background Cache Updates
```python
# Background task updates stale data without blocking requests
async def background_update(missing_timestamps):
    fresh_data = await fetch_missing_data(missing_timestamps)
    await cache_manager.cache_data(currency_pair, timeframe, fresh_data)
```

### 6. Cache Warming
```python
# Proactively warm cache for popular data
async def warm_popular_data():
    popular_pairs = ['btcusd', 'ethusd']
    for pair in popular_pairs:
        await cache_manager.warm_cache([pair], ['m1', 'm5', 'h1'])
```

## Configuration

### Enhanced `app/config.py`
```python
# Sources configuration
SOURCES = ['bitstamp', 'coindesk']

# Cache strategy settings
CACHE_STRATEGY = 'cache_first'  # 'cache_only', 'cache_first', 'fresh_first', 'background'
CACHE_HIT_THRESHOLD = 0.8  # Minimum cache hit ratio to consider cache healthy
MEMORY_CACHE_SIZE = 1000   # Maximum entries in memory cache

# Cache freshness rules (timeframe-specific TTL)
FRESHNESS_RULES = {
    'm1': {'max_age_minutes': 2, 'quality_bonus_minutes': 1},
    'm5': {'max_age_minutes': 6, 'quality_bonus_minutes': 2},
    'h1': {'max_age_minutes': 61, 'quality_bonus_minutes': 15},
    'd1': {'max_age_minutes': 1441, 'quality_bonus_minutes': 240}
}

# Background task settings
BACKGROUND_TASKS = {
    'enabled': True,
    'max_concurrent_updates': 3,
    'warming_schedule': {
        'enabled': True,
        'interval_minutes': 30,
        'popular_pairs': ['btcusd', 'ethusd'],
        'timeframes': ['m1', 'm5', 'h1']
    }
}

# Source priorities
SOURCE_PRIORITIES = {
    'bitstamp': 0.90,
    'coindesk': 0.85,
    'kraken': 0.88
}
```

## Benefits

### 1. Performance (Cache-First)
- **Sub-second response times** for cached data
- **Background updates** don't block user requests
- **Memory + Database caching** for optimal speed
- **Intelligent prefetching** for popular data

### 2. Reliability
- **Graceful degradation** - return stale data if sources fail
- **Multiple cache levels** - memory, database, source fallback
- **Quality-aware caching** - better data cached longer
- **Circuit breaker patterns** for failing sources

### 3. Scalability
- **Non-blocking architecture** - requests don't wait for slow sources
- **Background task queues** for updates
- **Easy source addition** without endpoint changes
- **Efficient resource utilization**

### 4. Data Quality
- **Multi-source aggregation** improves overall quality
- **Quality-weighted caching** prioritizes better data
- **Intelligent conflict resolution**
- **Comprehensive quality assessment**

### 5. User Experience
- **Fast response times** even when sources are slow
- **High availability** through caching
- **Consistent performance** regardless of source status
- **Real-time data** with background refresh

### 6. Maintainability
- **Clear separation of concerns**
- **Modular, extensible architecture**
- **Comprehensive monitoring and logging**
- **Easy configuration and tuning**

## Implementation Status

All files have been created with detailed comments explaining their intended functionality. The structure is ready for implementation with:

- ✅ Complete directory structure
- ✅ All component interfaces defined
- ✅ Comprehensive documentation
- ✅ Enhanced configuration
- ✅ Database schema design

## Next Steps

1. **Implement core components** starting with UnifiedDataManager
2. **Create unified database schema** and operations
3. **Implement translators** for existing sources
4. **Update endpoints** to use unified data
5. **Add comprehensive testing**
6. **Performance optimization**
7. **Add monitoring and alerting**

This architecture provides a solid foundation for a scalable, reliable, and maintainable multi-source data aggregation system.
