#!/usr/bin/env python3
"""
Test script for Kraken translator functionality
"""

import sys
import os
sys.path.append('/home/<USER>/PycharmProjects/dabot-ohlc')

from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
from app.core.translators.kraken_translator import KrakenTranslator

def test_kraken_integration():
    """Test the complete Kraken integration flow"""
    print("Testing Kraken Integration...")
    
    # Step 1: Fetch data from Kraken
    print("\n1. Fetching data from Kraken API...")
    raw_data = get_candles_from_kraken('btcusd', 'h1', 5)
    
    if raw_data is None or raw_data.empty:
        print("❌ Failed to fetch data from Kraken")
        return False
    
    print(f"✅ Successfully fetched {len(raw_data)} candles from Kraken")
    print("Raw data columns:", raw_data.columns.tolist())
    print("Raw data sample:")
    print(raw_data.head(2))
    
    # Step 2: Translate data using Kraken translator
    print("\n2. Translating data using KrakenTranslator...")
    translator = KrakenTranslator()
    
    try:
        translated_data = translator.translate(raw_data)
        
        if translated_data.empty:
            print("❌ Translation resulted in empty DataFrame")
            return False
        
        print(f"✅ Successfully translated {len(translated_data)} records")
        print("Translated data columns:", translated_data.columns.tolist())
        print("Translated data sample:")
        print(translated_data.head(2))
        
        # Step 3: Check translation quality
        print("\n3. Checking translation quality...")
        stats = translator.get_translation_statistics()
        print("Translation statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Step 4: Validate data quality
        print("\n4. Validating data quality...")
        required_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume', 'source', 'quality_score']
        missing_columns = [col for col in required_columns if col not in translated_data.columns]
        
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            return False
        
        # Check OHLC relationships
        ohlc_valid = True
        for idx, row in translated_data.iterrows():
            if not (row['high'] >= max(row['open'], row['close']) and 
                   row['low'] <= min(row['open'], row['close'])):
                print(f"❌ OHLC relationship violation at index {idx}")
                ohlc_valid = False
                break
        
        if ohlc_valid:
            print("✅ All OHLC relationships are valid")
        
        # Check quality scores
        avg_quality = translated_data['quality_score'].mean()
        print(f"✅ Average quality score: {avg_quality:.3f}")
        
        if avg_quality >= 0.8:
            print("✅ High quality data translation")
        elif avg_quality >= 0.6:
            print("⚠️  Medium quality data translation")
        else:
            print("❌ Low quality data translation")
        
        print("\n🎉 Kraken integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Translation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_kraken_integration()
    sys.exit(0 if success else 1)
